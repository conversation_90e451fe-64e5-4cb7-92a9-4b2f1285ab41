## 🧾 产品需求文档（PRD）

### 产品名称：智能表单自动填写助手（Smart Autofill Assistant）

---

### 一、产品背景与目标

在现代浏览体验中，用户频繁在网页中填写表单，如注册、登录、地址填写、支付信息等。传统 autofill 技术依赖固定字段名匹配，面对结构复杂、异构或动态生成的表单时效果不佳。

随着 Google flash-lite-2.5 等轻量级大模型的发展，页面语义理解和结构解析能力大幅增强，有机会突破传统 autofill 的局限。
本产品旨在通过该模型实现高准确度、强泛化能力的智能自动填写体验，提升用户效率与填写准确率。

---

### 二、产品功能定义

#### 1. 页面信息提取（Page Analysis）

* **触发方式**：用户点击插件或页面加载后自动触发（可配置）
* **技术手段**：

  * 读取当前页面 HTML DOM 结构
  * 构建简洁语义描述（Prompt-friendly JSON/Tree）
  * 提供上下文信息（例如页面类型、url、meta信息）

#### 2. 表单字段识别（Semantic Form Understanding）

* **模型调用**：调用 flash-lite-2.5 进行页面表单理解任务
* **目标输出**：

  * 表单字段的“语义标签”识别（如“邮箱地址”、“邮政编码”）
  * 字段位置（DOM selector）
  * 字段类型（输入框、下拉、单选、checkbox）

#### 3. 用户信息管理（User Profile Memory）

* **信息结构**：每条信息包含 `name`（标签名） 与 `info`（非结构化自然语言描述），结构极简。
* **数据结构样例**：

  ```json
  [
    {
      "name": "jaysean",
      "info": "我叫千岁，英文名是jaysean 出生于1989/07/31 邮箱jaysean<EMAIL>"
    },
    {
      "name": "xinyi han",
      "info": "我喜欢唱歌跳舞，叫韩心怡，最近在澳洲读design ,Unit 308, 119 Ross Street, Glebe, NSW 2037, Australia"
    }
  ]
  ```
* **存储方式**：使用浏览器的 localStorage
* **功能要求**：

  * 显示所有信息项的 name 标签供用户选择
  * 可添加、编辑、删除任意项
  * 编辑页中仅包含两个输入框：`name` 与 `info`
  * 不使用结构化字段或分组功能，保持界面简洁

#### 4. 自动填写逻辑（Autofill Engine）

* **字段匹配逻辑**：

  * 插件调用模型，将页面结构和用户选择的 info 一并发送给 flash-lite-2.5
  * 模型返回各表单字段所对应的填写值
* **数据注入方式**：

  * 使用 JavaScript 定位对应字段的 DOM 节点
  * 自动填充内容
  * 触发必要事件：`input`, `change`, `blur`

#### 5. 用户界面（Plugin UI）

* **弹出页面结构**：

  1. **信息选择页面**（点击插件图标后默认弹出）

     * 展示所有用户配置项 `name`
     * 可切换查看不同 info 内容
     * 提供“添加”、“编辑”、“删除”按钮
     * 提供“应用此信息进行自动填写”按钮
  2. **信息编辑页面**：

     * 两个输入框：

       * `name`（此信息的识别名称）
       * `info`（整段自然语言信息）
     * 保存并返回

#### 6. 权限与隐私（Security & Privacy）

* 所有用户信息保存在浏览器 localStorage 中
* 默认使用插件本地的 HTTP 接口与模型交互，无需部署后端服务
* 用户数据不上传，不进行远程存储
* 若用户自行配置远程模型地址与密钥，可手动切换

---

### 三、关键技术组件

| 模块        | 技术选型建议                                     |
| --------- | ------------------------------------------ |
| 模型服务      | Google flash-lite-2.5 本地调用接口（插件内 fetch 调用） |
| 页面 DOM 提取 | JavaScript + MutationObserver              |
| 插件架构      | Manifest V3 + WebExtension API             |
| 数据管理      | localStorage                               |
| 与模型交互     | 本地 fetch 请求 + prompt 构造                    |
| 表单注入      | JS 脚本注入 + 事件模拟                             |

---

### 四、使用流程（用户视角）

1. 用户安装插件，配置个人信息
2. 用户访问包含表单的网页，点击插件
3. 弹出信息选择页面，选择某一信息并点击“开始填写”
4. 插件分析页面结构 → 调用模型 → 匹配字段 → 自动填写
5. 用户可在页面上查看填写效果并提交表单

---

### 五、可扩展性与迭代方向

* 支持多语言识别与填写
* 表单填写后的验证逻辑支持（例如验证码识别、输入确认等）
* 支持对部分页面做“自动识别并填写”
* 插件内集成 prompt 可调试功能，便于高级用户调参
* 接入其他大模型服务：Gemini、Claude、OpenRouter 等