<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            padding: 15px;
            height: 100vh;
            overflow-y: auto;
        }

        .container {
            max-width: 100%;
            height: 100%;
        }

        .settings-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            height: calc(100% - 30px);
            overflow-y: auto;
        }

        .settings-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .settings-header h2 {
            color: #2d3748;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .settings-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .settings-section:last-child {
            border-bottom: none;
        }

        .section-title {
            color: #4a5568;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
        }

        .setting-label {
            color: #2d3748;
            font-size: 0.85rem;
            flex: 1;
        }

        .setting-description {
            color: #718096;
            font-size: 0.75rem;
            margin-top: 2px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #cbd5e0;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(20px);
        }

        .select-input {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.8rem;
            background: white;
            min-width: 100px;
        }

        .text-input {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.8rem;
            background: white;
            width: 120px;
        }

        .btn-small {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-small:hover {
            background: #5a67d8;
        }

        .btn-small.danger {
            background: #e53e3e;
        }

        .btn-small.danger:hover {
            background: #c53030;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #c6f6d5;
        }

        .status-indicator.offline {
            background: #fef5e7;
            color: #d69e2e;
            border-color: #fbd38d;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        .model-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            margin-top: 8px;
            font-size: 0.75rem;
            color: #718096;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="settings-container">
            <div class="settings-header">
                <h2>⚙️ Settings</h2>
            </div>

            <div class="settings-section">
                <div class="section-title">
                    🤖 AI Model Configuration
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Model Service</div>
                        <div class="setting-description">Choose AI model provider</div>
                    </div>
                    <select class="select-input" id="modelProvider">
                        <option value="local">Local Flash-Lite</option>
                        <option value="gemini">Google Gemini</option>
                        <option value="openai">OpenAI GPT</option>
                        <option value="claude">Anthropic Claude</option>
                    </select>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">API Status</div>
                    </div>
                    <div class="status-indicator" id="apiStatus">
                        <div class="status-dot"></div>
                        Connected
                    </div>
                </div>
                <div class="model-info">
                    💡 Local model provides better privacy but requires setup. Remote models need API keys.
                </div>
            </div>

            <div class="settings-section">
                <div class="section-title">
                    🔄 Auto-fill Behavior
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Auto-trigger on page load</div>
                        <div class="setting-description">Automatically analyze forms when page loads</div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Confirmation before fill</div>
                        <div class="setting-description">Ask before filling detected forms</div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Fill delay (ms)</div>
                        <div class="setting-description">Delay between field fills</div>
                    </div>
                    <input type="number" class="text-input" value="100" min="0" max="2000">
                </div>
            </div>

            <div class="settings-section">
                <div class="section-title">
                    🔒 Privacy & Security
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Store data locally only</div>
                        <div class="setting-description">Keep all data in browser storage</div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Clear data on uninstall</div>
                        <div class="setting-description">Remove all stored profiles when extension is removed</div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <div class="section-title">
                    🛠️ Advanced
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Debug mode</div>
                        <div class="setting-description">Show detailed logs and analysis</div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Export Settings</div>
                    </div>
                    <button class="btn-small" onclick="exportSettings()">📤 Export</button>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">Reset All Settings</div>
                    </div>
                    <button class="btn-small danger" onclick="resetSettings()">🔄 Reset</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSetting(element) {
            element.classList.toggle('active');
            // In a real implementation, this would save the setting to localStorage
        }

        function exportSettings() {
            alert('Settings exported to clipboard');
            // In a real implementation, this would export settings as JSON
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to default?')) {
                alert('Settings reset to default');
                // In a real implementation, this would reset all settings
                location.reload();
            }
        }

        // Model provider change handler
        document.getElementById('modelProvider').addEventListener('change', function() {
            const status = document.getElementById('apiStatus');
            if (this.value === 'local') {
                status.className = 'status-indicator';
                status.innerHTML = '<div class="status-dot"></div>Connected';
            } else {
                status.className = 'status-indicator offline';
                status.innerHTML = '<div class="status-dot"></div>API Key Required';
            }
        });
    </script>
</body>
</html>
