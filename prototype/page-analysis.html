<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            padding: 15px;
            height: 100vh;
            overflow-y: auto;
        }

        .container {
            max-width: 100%;
            height: 100%;
        }

        .analysis-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            height: calc(100% - 30px);
            overflow-y: auto;
        }

        .analysis-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .analysis-header h2 {
            color: #2d3748;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .page-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 15px;
        }

        .page-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 0.8rem;
        }

        .page-info-item:last-child {
            margin-bottom: 0;
        }

        .page-info-label {
            color: #4a5568;
            font-weight: 600;
        }

        .page-info-value {
            color: #718096;
            max-width: 60%;
            text-align: right;
            word-break: break-all;
        }

        .fields-section {
            margin-bottom: 15px;
        }

        .section-title {
            color: #4a5568;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .field-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .field-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .field-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }

        .field-name {
            color: #2d3748;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .field-type {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .field-type.select {
            background: #38a169;
        }

        .field-type.textarea {
            background: #d69e2e;
        }

        .field-type.checkbox {
            background: #9f7aea;
        }

        .field-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 0.75rem;
        }

        .field-detail {
            display: flex;
            justify-content: space-between;
        }

        .field-detail-label {
            color: #718096;
        }

        .field-detail-value {
            color: #4a5568;
            font-weight: 500;
            max-width: 60%;
            text-align: right;
            word-break: break-all;
        }

        .confidence-bar {
            background: #e2e8f0;
            border-radius: 4px;
            height: 4px;
            overflow: hidden;
            margin-top: 4px;
        }

        .confidence-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .confidence-high {
            background: #48bb78;
        }

        .confidence-medium {
            background: #d69e2e;
        }

        .confidence-low {
            background: #e53e3e;
        }

        .stats-section {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            color: #667eea;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .stat-label {
            color: #4a5568;
            font-size: 0.7rem;
            margin-top: 2px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .empty-state {
            text-align: center;
            padding: 30px 20px;
            color: #718096;
        }

        .empty-state h3 {
            margin-bottom: 8px;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="analysis-container">
            <div class="analysis-header">
                <h2>🔍 Page Analysis</h2>
            </div>

            <div class="page-info">
                <div class="page-info-item">
                    <span class="page-info-label">URL:</span>
                    <span class="page-info-value">https://example.com/register</span>
                </div>
                <div class="page-info-item">
                    <span class="page-info-label">Page Type:</span>
                    <span class="page-info-value">Registration Form</span>
                </div>
                <div class="page-info-item">
                    <span class="page-info-label">Last Analyzed:</span>
                    <span class="page-info-value">2 minutes ago</span>
                </div>
            </div>

            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">7</div>
                        <div class="stat-label">Fields Found</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Fillable</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">92%</div>
                        <div class="stat-label">Confidence</div>
                    </div>
                </div>
            </div>

            <div class="fields-section">
                <div class="section-title">
                    📝 Detected Form Fields
                </div>

                <div class="field-item">
                    <div class="field-header">
                        <span class="field-name">Email Address</span>
                        <span class="field-type">input</span>
                    </div>
                    <div class="field-details">
                        <div class="field-detail">
                            <span class="field-detail-label">Selector:</span>
                            <span class="field-detail-value">#email</span>
                        </div>
                        <div class="field-detail">
                            <span class="field-detail-label">Confidence:</span>
                            <span class="field-detail-value">98%</span>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-high" style="width: 98%"></div>
                    </div>
                </div>

                <div class="field-item">
                    <div class="field-header">
                        <span class="field-name">Full Name</span>
                        <span class="field-type">input</span>
                    </div>
                    <div class="field-details">
                        <div class="field-detail">
                            <span class="field-detail-label">Selector:</span>
                            <span class="field-detail-value">.name-field</span>
                        </div>
                        <div class="field-detail">
                            <span class="field-detail-label">Confidence:</span>
                            <span class="field-detail-value">95%</span>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-high" style="width: 95%"></div>
                    </div>
                </div>

                <div class="field-item">
                    <div class="field-header">
                        <span class="field-name">Phone Number</span>
                        <span class="field-type">input</span>
                    </div>
                    <div class="field-details">
                        <div class="field-detail">
                            <span class="field-detail-label">Selector:</span>
                            <span class="field-detail-value">input[type="tel"]</span>
                        </div>
                        <div class="field-detail">
                            <span class="field-detail-label">Confidence:</span>
                            <span class="field-detail-value">87%</span>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-medium" style="width: 87%"></div>
                    </div>
                </div>

                <div class="field-item">
                    <div class="field-header">
                        <span class="field-name">Country</span>
                        <span class="field-type select">select</span>
                    </div>
                    <div class="field-details">
                        <div class="field-detail">
                            <span class="field-detail-label">Selector:</span>
                            <span class="field-detail-value">#country-select</span>
                        </div>
                        <div class="field-detail">
                            <span class="field-detail-label">Confidence:</span>
                            <span class="field-detail-value">92%</span>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-high" style="width: 92%"></div>
                    </div>
                </div>

                <div class="field-item">
                    <div class="field-header">
                        <span class="field-name">Address</span>
                        <span class="field-type textarea">textarea</span>
                    </div>
                    <div class="field-details">
                        <div class="field-detail">
                            <span class="field-detail-label">Selector:</span>
                            <span class="field-detail-value">#address</span>
                        </div>
                        <div class="field-detail">
                            <span class="field-detail-label">Confidence:</span>
                            <span class="field-detail-value">89%</span>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill confidence-medium" style="width: 89%"></div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-secondary" onclick="refreshAnalysis()">🔄 Refresh</button>
                <button class="btn btn-primary" onclick="exportAnalysis()">📤 Export</button>
            </div>
        </div>
    </div>

    <script>
        function refreshAnalysis() {
            alert('Refreshing page analysis...');
            // In a real implementation, this would re-analyze the current page
        }

        function exportAnalysis() {
            alert('Analysis data exported to clipboard');
            // In a real implementation, this would export the analysis results
        }

        // Simulate real-time updates
        setInterval(() => {
            const lastAnalyzed = document.querySelector('.page-info-value:last-child');
            const minutes = Math.floor(Math.random() * 10) + 1;
            lastAnalyzed.textContent = `${minutes} minutes ago`;
        }, 30000);
    </script>
</body>
</html>
